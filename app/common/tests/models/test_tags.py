"""Concise tests for common.models.tags module."""

import uuid
from datetime import date, datetime
from typing import Any, Dict

import pytest
from pydantic import ValidationError

from common.models.tags import (
    LiteLLMDailyTagSpend,
    TagUsageSummary,
    TagUsageDetail,
    TagUsageByDate,
    TagUsageByModel,
    TagUsageResponse
)


class TestLiteLLMDailyTagSpend:
    """Test cases for LiteLLMDailyTagSpend SQLAlchemy model."""

    def test_model_attributes(self) -> None:
        """Test model has required attributes and table configuration."""
        model = LiteLLMDailyTagSpend

        # Test table configuration
        assert model.__tablename__ == "LiteLLM_DailyTagSpend"
        assert model.__table_args__["schema"] == "public"

        # Test actual columns exist (based on the source code)
        actual_columns = ["date", "tag", "spend", "api_requests", "prompt_tokens", "completion_tokens"]
        for column in actual_columns:
            assert hasattr(model, column), f"Model should have column: {column}"

    def test_table_structure(self) -> None:
        """Test SQLAlchemy table structure."""
        model = LiteLLMDailyTagSpend

        # Verify it's a proper SQLAlchemy model
        assert hasattr(model, '__table__')
        assert hasattr(model, '__mapper__')

        # Test table name and schema
        table = model.__table__
        assert table.name == "LiteLLM_DailyTagSpend"
        assert table.schema == "public"

        # Verify columns exist in table
        column_names = [col.name for col in table.columns]
        expected_columns = ["date", "tag", "spend", "api_requests", "prompt_tokens", "completion_tokens"]
        for expected_col in expected_columns:
            assert expected_col in column_names, f"Table should have column: {expected_col}"


class TestTagUsageSummary:
    """Test cases for TagUsageSummary model."""

    def test_creation(self, sample_tag_usage_data: Dict[str, Any]) -> None:
        """Test creating TagUsageSummary with valid data."""
        summary = TagUsageSummary(**sample_tag_usage_data)

        assert summary.tag == sample_tag_usage_data["tag"]
        assert summary.total_spend == sample_tag_usage_data["total_spend"]
        assert summary.total_requests == sample_tag_usage_data["total_requests"]
        assert summary.total_tokens == sample_tag_usage_data["total_tokens"]
        assert summary.success_rate == sample_tag_usage_data["success_rate"]
        assert summary.date_range == sample_tag_usage_data["date_range"]

    def test_missing_required_fields(self) -> None:
        """Test TagUsageSummary validation with missing required fields."""
        # Missing tag
        with pytest.raises(ValidationError, match=r"Field required") as exc_info:
            TagUsageSummary(total_spend=100.0)

        assert "tag" in str(exc_info.value)

        # Test other required fields
        required_fields = ["tag", "total_spend", "total_requests", "total_tokens", "success_rate", "date_range"]
        for field in required_fields:
            test_data = {
                "tag": "test",
                "total_spend": 100.0,
                "total_requests": 1000,
                "total_tokens": 50000,
                "success_rate": 95.5,
                "date_range": "2023-10-01"
            }
            del test_data[field]

            with pytest.raises(ValidationError):
                TagUsageSummary(**test_data)

    def test_field_types(self) -> None:
        """Test TagUsageSummary field type validation."""
        summary = TagUsageSummary(
            tag="test-tag",
            total_spend=100.50,
            total_requests=1000,
            total_tokens=50000,
            success_rate=95.5,
            date_range="2023-10-01 to 2023-10-07"
        )

        assert isinstance(summary.tag, str)
        assert isinstance(summary.total_spend, float)
        assert isinstance(summary.total_requests, int)
        assert isinstance(summary.total_tokens, int)
        assert isinstance(summary.success_rate, float)
        assert isinstance(summary.date_range, str)

    def test_edge_case_values(self, edge_case_strings: Dict[str, str]) -> None:
        """Test TagUsageSummary with edge case values."""
        # Test with unicode tag
        summary1 = TagUsageSummary(
            tag=edge_case_strings["unicode"],
            total_spend=0.0,
            total_requests=0,
            total_tokens=0,
            success_rate=0.0,
            date_range="2023-10-01"
        )
        assert summary1.tag == edge_case_strings["unicode"]

        # Test with special characters in tag
        summary2 = TagUsageSummary(
            tag=edge_case_strings["special_chars"],
            total_spend=999999.99,
            total_requests=999999,
            total_tokens=999999,
            success_rate=100.0,
            date_range=edge_case_strings["unicode"]
        )
        assert summary2.tag == edge_case_strings["special_chars"]

    def test_json_serialization(self, sample_tag_usage_data: Dict[str, Any]) -> None:
        """Test TagUsageSummary JSON serialization."""
        summary = TagUsageSummary(**sample_tag_usage_data)

        # Test dict serialization
        json_dict = summary.model_dump()
        assert json_dict["tag"] == sample_tag_usage_data["tag"]
        assert json_dict["total_spend"] == sample_tag_usage_data["total_spend"]

        # Test JSON string serialization
        json_str = summary.model_dump_json()
        assert sample_tag_usage_data["tag"] in json_str
        assert str(sample_tag_usage_data["total_spend"]) in json_str


class TestTagUsageDetail:
    """Test cases for TagUsageDetail model."""

    def test_creation(self):
        """Test creating TagUsageDetail with valid data."""
        from datetime import datetime
        detail = TagUsageDetail(
            id=uuid.uuid4(),
            tag="test-tag",
            date="2023-10-01",
            api_key="test-key",
            model="gpt-4",
            model_group="openai",
            custom_llm_provider="openai",
            prompt_tokens=100,
            completion_tokens=50,
            cache_read_input_tokens=0,
            cache_creation_input_tokens=0,
            spend=0.05,
            api_requests=1,
            successful_requests=1,
            failed_requests=0,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        assert isinstance(detail.id, uuid.UUID)
        assert detail.tag == "test-tag"
        assert detail.model == "gpt-4"
        assert detail.spend == 0.05
        assert detail.prompt_tokens == 100
        assert detail.completion_tokens == 50

    def test_json_serialization(self):
        """Test TagUsageDetail JSON serialization."""
        from datetime import datetime
        detail = TagUsageDetail(
            id=uuid.uuid4(),
            tag="test-tag",
            date="2023-10-01",
            api_key="test-key",
            model="gpt-3.5-turbo",
            model_group="openai",
            custom_llm_provider="openai",
            prompt_tokens=80,
            completion_tokens=20,
            cache_read_input_tokens=0,
            cache_creation_input_tokens=0,
            spend=0.02,
            api_requests=1,
            successful_requests=1,
            failed_requests=0,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        json_data = detail.model_dump()
        assert "id" in json_data
        assert json_data["model"] == "gpt-3.5-turbo"
        assert json_data["spend"] == 0.02
        assert json_data["prompt_tokens"] == 80


class TestTagUsageByDate:
    """Test cases for TagUsageByDate model."""

    def test_creation(self):
        """Test creating TagUsageByDate with valid data."""
        usage = TagUsageByDate(
            date="2023-10-01",
            spend=125.50,
            api_requests=500,
            total_tokens=25000,
            success_rate=99.2
        )

        assert usage.date == "2023-10-01"
        assert usage.spend == 125.50
        assert usage.api_requests == 500
        assert usage.total_tokens == 25000
        assert usage.success_rate == 99.2


class TestTagUsageByModel:
    """Test cases for TagUsageByModel model."""

    def test_creation(self):
        """Test creating TagUsageByModel with valid data."""
        usage = TagUsageByModel(
            model="gpt-4",
            model_group="openai",
            custom_llm_provider="openai",
            total_spend=875.52,
            total_requests=3500,
            total_tokens=175000,
            success_rate=99.9
        )

        assert usage.model == "gpt-4"
        assert usage.model_group == "openai"
        assert usage.custom_llm_provider == "openai"
        assert usage.total_spend == 875.52
        assert usage.total_requests == 3500
        assert usage.total_tokens == 175000
        assert usage.success_rate == 99.9


class TestTagUsageResponse:
    """Test cases for TagUsageResponse model."""

    def test_creation(self):
        """Test creating TagUsageResponse with all components."""
        summary = TagUsageSummary(
            tag="production-api",
            total_spend=1250.75,
            total_requests=5000,
            total_tokens=250000,
            success_rate=99.8,
            date_range="2023-10-01 to 2023-10-07"
        )

        daily_data = [
            TagUsageByDate(
                date="2023-10-01",
                spend=178.68,
                api_requests=714,
                total_tokens=35714,
                success_rate=99.7
            )
        ]

        model_breakdown = [
            TagUsageByModel(
                model="gpt-4",
                model_group="openai",
                custom_llm_provider="openai",
                total_spend=875.52,
                total_requests=3500,
                total_tokens=175000,
                success_rate=99.9
            )
        ]

        response = TagUsageResponse(
            tag="production-api",
            summary=summary,
            daily_data=daily_data,
            model_breakdown=model_breakdown,
            total_records=5000
        )

        assert response.tag == "production-api"
        assert response.summary == summary
        assert len(response.daily_data) == 1
        assert len(response.model_breakdown) == 1
        assert response.total_records == 5000

    def test_json_serialization(self):
        """Test TagUsageResponse JSON serialization."""
        summary = TagUsageSummary(
            tag="test-tag",
            total_spend=100.0,
            total_requests=100,
            total_tokens=10000,
            success_rate=99.0,
            date_range="2023-10-01"
        )

        response = TagUsageResponse(
            tag="test-tag",
            summary=summary,
            daily_data=[],
            model_breakdown=[],
            total_records=0
        )

        json_data = response.model_dump()
        assert json_data["tag"] == "test-tag"
        assert "summary" in json_data
        assert json_data["daily_data"] == []
        assert json_data["model_breakdown"] == []
        assert json_data["total_records"] == 0


class TestTagsModelsIntegration:
    """Integration tests for tag models."""

    def test_model_serialization_consistency(self):
        """Test that all models serialize consistently."""
        summary = TagUsageSummary(
            tag="integration-test",
            total_spend=500.0,
            total_requests=1000,
            total_tokens=50000,
            success_rate=98.5,
            date_range="2023-10-01"
        )

        daily = TagUsageByDate(
            date="2023-10-01",
            spend=500.0,
            api_requests=1000,
            total_tokens=50000,
            success_rate=98.5
        )

        model = TagUsageByModel(
            model="gpt-3.5-turbo",
            model_group="openai",
            custom_llm_provider="openai",
            total_spend=500.0,
            total_requests=1000,
            total_tokens=50000,
            success_rate=98.5
        )

        # All models should serialize without errors
        summary_json = summary.model_dump()
        daily_json = daily.model_dump()
        model_json = model.model_dump()

        assert all(isinstance(data, dict) for data in [summary_json, daily_json, model_json])
        # Check that spend-related fields exist (different field names in different models)
        assert "total_spend" in summary_json
        assert "spend" in daily_json
        assert "total_spend" in model_json
