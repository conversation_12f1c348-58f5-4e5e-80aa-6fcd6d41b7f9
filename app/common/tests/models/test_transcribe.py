"""Concise tests for common.models.transcribe module."""

import io
from typing import Any, Dict

import pytest
from fastapi import UploadFile
from pydantic import ValidationError

from common.models.transcribe import TranscriptionRequest, TranscriptionJobResponse


class TestTranscriptionRequest:
    """Test cases for TranscriptionRequest model."""

    def test_with_upload_file(self) -> None:
        """Test TranscriptionRequest with UploadFile."""
        upload_file = UploadFile(
            filename="test_audio.mp3",
            file=io.BytesIO(b"mock audio content")
        )

        request = TranscriptionRequest(
            file=upload_file,
            language_code="en-US",
            media_format="mp3",
            user="test_user"
        )

        assert request.file == upload_file
        assert request.language_code == "en-US"
        assert request.media_format == "mp3"
        assert request.user == "test_user"

    def test_with_string_url(self, sample_transcription_data: Dict[str, Any]) -> None:
        """Test TranscriptionRequest with S3 URL string."""
        request = TranscriptionRequest(**sample_transcription_data)

        assert request.file == sample_transcription_data["s3_url"]
        assert request.language_code == sample_transcription_data["language_code"]
        assert request.media_format == sample_transcription_data["media_format"]
        assert request.user == sample_transcription_data["user"]

    def test_minimal_fields(self) -> None:
        """Test TranscriptionRequest with only required field."""
        s3_url = "s3://bucket/audio/file.mp3"
        request = TranscriptionRequest(file=s3_url)

        assert request.file == s3_url
        assert request.language_code is None
        assert request.media_format is None
        assert request.user is None

    def test_missing_file_validation(self) -> None:
        """Test TranscriptionRequest validation without file."""
        with pytest.raises(ValidationError, match=r"Field required") as exc_info:
            TranscriptionRequest(language_code="en-US", user="test")

        assert "file" in str(exc_info.value)

    def test_various_file_formats(self) -> None:
        """Test TranscriptionRequest with various file formats."""
        file_formats = [
            ("mp3", "s3://bucket/audio.mp3"),
            ("wav", "s3://bucket/audio.wav"),
            ("m4a", "s3://bucket/audio.m4a"),
            ("flac", "s3://bucket/audio.flac"),
            ("ogg", "s3://bucket/audio.ogg")
        ]

        for format_type, url in file_formats:
            request = TranscriptionRequest(
                file=url,
                media_format=format_type,
                language_code="en-US"
            )
            assert request.file == url
            assert request.media_format == format_type

    def test_language_codes(self) -> None:
        """Test TranscriptionRequest with various language codes."""
        language_codes = [
            "en-US", "en-GB", "es-ES", "fr-FR", "de-DE",
            "it-IT", "pt-BR", "ja-JP", "ko-KR", "zh-CN"
        ]

        for lang_code in language_codes:
            request = TranscriptionRequest(
                file="s3://bucket/audio.wav",
                language_code=lang_code
            )
            assert request.language_code == lang_code

    def test_edge_case_urls(self, edge_case_strings: Dict[str, Any]) -> None:
        """Test TranscriptionRequest with edge case URLs."""
        # Very long URL
        long_url = "s3://bucket/" + "a" * 500 + ".wav"
        request1 = TranscriptionRequest(file=long_url)
        assert request1.file == long_url

        # URL with special characters
        special_url = "s3://bucket/audio-file_with-special.chars.wav"
        request2 = TranscriptionRequest(file=special_url)
        assert request2.file == special_url

        # Unicode in filename
        unicode_url = "s3://bucket/音频文件.wav"
        request3 = TranscriptionRequest(file=unicode_url)
        assert request3.file == unicode_url

    def test_json_serialization(self, sample_transcription_data: Dict[str, Any]) -> None:
        """Test TranscriptionRequest JSON serialization."""
        request = TranscriptionRequest(
            file=sample_transcription_data["s3_url"],
            language_code=sample_transcription_data["language_code"],
            media_format=sample_transcription_data["media_format"],
            user=sample_transcription_data["user"]
        )

        json_data = request.model_dump()
        assert json_data["file"] == sample_transcription_data["s3_url"]
        assert json_data["language_code"] == sample_transcription_data["language_code"]
        assert json_data["media_format"] == sample_transcription_data["media_format"]
        assert json_data["user"] == sample_transcription_data["user"]

        # Test JSON string serialization
        json_str = request.model_dump_json()
        assert sample_transcription_data["s3_url"] in json_str
        assert sample_transcription_data["language_code"] in json_str

        # Test excluding None values
        minimal_request = TranscriptionRequest(file=sample_transcription_data["s3_url"])
        json_dict_exclude_none = minimal_request.model_dump(exclude_none=True)
        assert "language_code" not in json_dict_exclude_none
        assert "media_format" not in json_dict_exclude_none
        assert "user" not in json_dict_exclude_none

    def test_as_form_method(self) -> None:
        """Test TranscriptionRequest.as_form class method."""
        upload_file = UploadFile(
            filename="test.mp3",
            file=io.BytesIO(b"audio content")
        )

        request = TranscriptionRequest.as_form(
            file=upload_file,
            language_code="en-US",
            media_format="mp3",
            user="test_user"
        )

        assert request.file == upload_file
        assert request.language_code == "en-US"
        assert request.media_format == "mp3"
        assert request.user == "test_user"

    def test_as_form_with_url(self) -> None:
        """Test as_form with file_url parameter."""
        file_url = "s3://bucket/audio.mp3"

        request = TranscriptionRequest.as_form(
            file_url=file_url,
            language_code="es-ES",
            media_format="mp3"
        )

        assert request.file == file_url
        assert request.language_code == "es-ES"
        assert request.media_format == "mp3"
        assert request.user is None

    def test_as_form_missing_file_and_url(self) -> None:
        """Test as_form validation when both file and file_url are missing."""
        with pytest.raises(ValueError, match=r"file or file_url must be provided") as exc_info:
            TranscriptionRequest.as_form(language_code="en-US", user="test")

        assert "file or file_url must be provided" in str(exc_info.value)

    def test_as_form_both_file_and_url(self) -> None:
        """Test as_form behavior when both file and file_url are provided."""
        upload_file = UploadFile(
            filename="test.mp3",
            file=io.BytesIO(b"audio content")
        )
        file_url = "s3://bucket/audio.mp3"

        # Should prioritize file over file_url
        request = TranscriptionRequest.as_form(
            file=upload_file,
            file_url=file_url,
            language_code="en-US"
        )

        assert request.file == upload_file  # file takes precedence
        assert request.language_code == "en-US"


class TestTranscriptionJobResponse:
    """Test cases for TranscriptionJobResponse model."""

    def test_creation(self, sample_job_ids: Dict[str, str]) -> None:
        """Test creating TranscriptionJobResponse."""
        job_id = sample_job_ids["transcription_job"]
        response = TranscriptionJobResponse(job_id=job_id)

        assert response.job_id == job_id
        assert isinstance(response.job_id, str)

    def test_missing_job_id_validation(self) -> None:
        """Test TranscriptionJobResponse validation without job_id."""
        with pytest.raises(ValidationError, match=r"Field required") as exc_info:
            TranscriptionJobResponse()

        assert "job_id" in str(exc_info.value)

    def test_json_serialization(self, sample_job_ids: Dict[str, str]) -> None:
        """Test TranscriptionJobResponse JSON serialization."""
        job_id = sample_job_ids["transcription_job"]
        response = TranscriptionJobResponse(job_id=job_id)

        json_data = response.model_dump()
        assert json_data["job_id"] == job_id

        json_str = response.model_dump_json()
        assert job_id in json_str
        assert '"job_id"' in json_str

    def test_equality(self) -> None:
        """Test TranscriptionJobResponse equality comparison."""
        job_id = "same-job-id"
        response1 = TranscriptionJobResponse(job_id=job_id)
        response2 = TranscriptionJobResponse(job_id=job_id)
        response3 = TranscriptionJobResponse(job_id="different-job-id")

        assert response1 == response2
        assert response1 != response3
        assert hash(response1) == hash(response2)
        assert hash(response1) != hash(response3)

    def test_various_job_id_formats(self, edge_case_strings: Dict[str, str]) -> None:
        """Test TranscriptionJobResponse with various job ID formats."""
        # UUID format
        uuid_job_id = "12345678-1234-5678-9abc-123456789012"
        response1 = TranscriptionJobResponse(job_id=uuid_job_id)
        assert response1.job_id == uuid_job_id

        # Prefixed format
        prefixed_job_id = "transcribe-job-2023-10-01-001"
        response2 = TranscriptionJobResponse(job_id=prefixed_job_id)
        assert response2.job_id == prefixed_job_id

        # Unicode characters
        unicode_job_id = f"job-{edge_case_strings['unicode']}-001"
        response3 = TranscriptionJobResponse(job_id=unicode_job_id)
        assert response3.job_id == unicode_job_id

        # Very long job ID
        long_job_id = "transcribe-job-" + "a" * 100
        response4 = TranscriptionJobResponse(job_id=long_job_id)
        assert response4.job_id == long_job_id

    def test_empty_job_id_validation(self) -> None:
        """Test TranscriptionJobResponse validation with empty job_id."""
        with pytest.raises(ValidationError):
            TranscriptionJobResponse(job_id="")

    def test_serialization_roundtrip(self, sample_job_ids: Dict[str, str]) -> None:
        """Test serialization roundtrip for TranscriptionJobResponse."""
        import json

        original_response = TranscriptionJobResponse(job_id=sample_job_ids["transcription_job"])

        # Serialize and deserialize
        json_str = original_response.model_dump_json()
        json_data = json.loads(json_str)
        recreated_response = TranscriptionJobResponse(**json_data)

        assert recreated_response.job_id == original_response.job_id
        assert recreated_response == original_response


class TestTranscribeModelsIntegration:
    """Integration tests for transcribe models."""

    def test_request_response_workflow(self, sample_transcription_data: Dict[str, Any]) -> None:
        """Test realistic transcription workflow."""
        # Create request
        request = TranscriptionRequest(
            file=sample_transcription_data["s3_url"],
            language_code=sample_transcription_data["language_code"],
            media_format=sample_transcription_data["media_format"],
            user=sample_transcription_data["user"]
        )

        # Simulate job submission response
        job_id = "transcription_job_2023_10_01_001"
        response = TranscriptionJobResponse(job_id=job_id)

        # Verify data consistency
        assert request.file == sample_transcription_data["s3_url"]
        assert request.language_code == sample_transcription_data["language_code"]
        assert request.media_format == sample_transcription_data["media_format"]
        assert request.user == sample_transcription_data["user"]
        assert response.job_id == job_id

    def test_form_submission_workflow(self) -> None:
        """Test form-based transcription workflow."""
        # Simulate form submission with file upload
        upload_file = UploadFile(
            filename="meeting_recording.wav",
            file=io.BytesIO(b"mock audio data for meeting recording")
        )

        # Create request using as_form method
        request = TranscriptionRequest.as_form(
            file=upload_file,
            language_code="en-US",
            media_format="wav",
            user="meeting_organizer"
        )

        # Verify request creation
        assert request.file == upload_file
        assert request.language_code == "en-US"
        assert request.media_format == "wav"
        assert request.user == "meeting_organizer"

        # Simulate job creation
        job_id = f"transcribe-{upload_file.filename}-{hash(request.user)}"
        response = TranscriptionJobResponse(job_id=job_id)

        assert response.job_id == job_id

    def test_url_based_workflow(self, sample_transcription_data: Dict[str, Any]) -> None:
        """Test URL-based transcription workflow."""
        # Create request using as_form with URL
        request = TranscriptionRequest.as_form(
            file_url=sample_transcription_data["s3_url"],
            language_code=sample_transcription_data["language_code"],
            media_format=sample_transcription_data["media_format"],
            user=sample_transcription_data["user"]
        )

        # Verify request creation
        assert request.file == sample_transcription_data["s3_url"]
        assert request.language_code == sample_transcription_data["language_code"]

        # Simulate job creation with URL-based naming
        job_id = f"transcribe-url-{hash(sample_transcription_data['s3_url'])}"
        response = TranscriptionJobResponse(job_id=job_id)

        assert response.job_id == job_id

    def test_serialization_consistency(self, sample_transcription_data: Dict[str, Any]) -> None:
        """Test serialization consistency across models."""
        # Create request
        request = TranscriptionRequest(**{
            "file": sample_transcription_data["s3_url"],
            "language_code": sample_transcription_data["language_code"],
            "media_format": sample_transcription_data["media_format"],
            "user": sample_transcription_data["user"]
        })

        # Create response
        response = TranscriptionJobResponse(job_id="test-job-123")

        # Test serialization
        request_json = request.model_dump()
        response_json = response.model_dump()

        # Verify both serialize to dicts
        assert isinstance(request_json, dict)
        assert isinstance(response_json, dict)

        # Verify required fields are present
        assert "file" in request_json
        assert "job_id" in response_json

        # Test JSON string serialization
        request_json_str = request.model_dump_json()
        response_json_str = response.model_dump_json()

        assert isinstance(request_json_str, str)
        assert isinstance(response_json_str, str)
        assert sample_transcription_data["s3_url"] in request_json_str
        assert "test-job-123" in response_json_str

    def test_error_handling_workflow(self) -> None:
        """Test error handling in transcription workflow."""
        # Test invalid request creation
        with pytest.raises(ValidationError):
            TranscriptionRequest()  # Missing file

        with pytest.raises(ValidationError):
            TranscriptionJobResponse()  # Missing job_id

        # Test as_form error handling
        with pytest.raises(ValueError):
            TranscriptionRequest.as_form(language_code="en-US")  # Missing file and file_url

        # Test successful creation after fixing errors
        valid_request = TranscriptionRequest(file="s3://bucket/audio.wav")
        valid_response = TranscriptionJobResponse(job_id="valid-job-123")

        assert valid_request.file == "s3://bucket/audio.wav"
        assert valid_response.job_id == "valid-job-123"

    def test_realistic_enterprise_workflow(self) -> None:
        """Test realistic enterprise transcription workflow."""
        # Enterprise scenario: batch processing of meeting recordings
        meeting_files = [
            "s3://enterprise-audio/2023/Q4/board-meeting-oct.wav",
            "s3://enterprise-audio/2023/Q4/team-standup-oct.mp3",
            "s3://enterprise-audio/2023/Q4/client-call-oct.m4a"
        ]

        requests = []
        responses = []

        for i, file_url in enumerate(meeting_files):
            # Create transcription request
            request = TranscriptionRequest(
                file=file_url,
                language_code="en-US",
                media_format=file_url.split('.')[-1],
                user=f"enterprise_user_{i+1}"
            )
            requests.append(request)

            # Simulate job submission
            job_id = f"enterprise-transcribe-{i+1}-{hash(file_url)}"
            response = TranscriptionJobResponse(job_id=job_id)
            responses.append(response)

        # Verify all requests and responses
        assert len(requests) == len(meeting_files)
        assert len(responses) == len(meeting_files)

        for i, (request, response) in enumerate(zip(requests, responses)):
            assert request.file == meeting_files[i]
            assert request.language_code == "en-US"
            assert request.user == f"enterprise_user_{i+1}"
            assert response.job_id.startswith("enterprise-transcribe-")
