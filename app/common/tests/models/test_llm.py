"""Concise tests for common.models.llm module."""

from typing import Any, Dict, List

import pytest
from pydantic import ValidationError

from common.models.llm import CompletionRequest, ChatCompletionRequest


class TestCompletionRequest:
    """Test cases for CompletionRequest model."""

    def test_minimal_creation(self, sample_completion_request: Dict[str, Any]) -> None:
        """Test creating CompletionRequest with minimal required fields."""
        # Use fixture data but test minimal creation
        request = CompletionRequest(
            model=sample_completion_request["model"]
        )

        assert request.model == sample_completion_request["model"]
        assert request.files is None
        assert request.max_tokens is None
        assert request.temperature is None
        assert request.top_p is None
        assert request.n is None
        assert request.stream is None
        assert request.stop is None
        assert request.presence_penalty is None
        assert request.frequency_penalty is None
        assert request.logit_bias is None
        assert request.user is None

    def test_all_fields(self, sample_completion_request: Dict[str, Any]) -> None:
        """Test CompletionRequest with all fields populated."""
        request = CompletionRequest(
            model=sample_completion_request["model"],
            messages=sample_completion_request["messages"],
            max_tokens=sample_completion_request["max_tokens"],
            temperature=sample_completion_request["temperature"],
            top_p=0.9,
            n=1,
            stream=False,
            logprobs=None,
            stop=["END", "STOP"],
            presence_penalty=0.0,
            frequency_penalty=0.0,
            logit_bias={"token_123": 0.5},
            user="test_user",
            files=["s3://bucket/doc.pdf", "s3://bucket/doc2.txt"]
        )

        assert request.model == sample_completion_request["model"]
        assert request.messages == sample_completion_request["messages"]
        assert request.max_tokens == sample_completion_request["max_tokens"]
        assert request.temperature == sample_completion_request["temperature"]
        assert request.top_p == 0.9
        assert request.n == 1
        assert request.stream is False
        assert request.stop == ["END", "STOP"]
        assert request.presence_penalty == 0.0
        assert request.frequency_penalty == 0.0
        assert request.logit_bias == {"token_123": 0.5}
        assert request.files == ["s3://bucket/doc.pdf", "s3://bucket/doc2.txt"]
        assert request.user == "test_user"

    def test_missing_required_fields(self) -> None:
        """Test CompletionRequest validation with missing required fields."""
        # LiteLLM CompletionRequest only requires 'model', not 'prompt'
        with pytest.raises(ValidationError, match=r"Field required") as exc_info:
            CompletionRequest()  # Missing model

        assert "model" in str(exc_info.value)

        # Test that model alone is sufficient (no prompt required)
        request = CompletionRequest(model="gpt-3.5-turbo")
        assert request.model == "gpt-3.5-turbo"

    def test_json_serialization(self) -> None:
        """Test CompletionRequest JSON serialization."""
        request = CompletionRequest(
            model="gpt-3.5-turbo",
            messages=["Test message"],
            max_tokens=100,
            temperature=0.5
        )

        json_data = request.model_dump()
        assert json_data["model"] == "gpt-3.5-turbo"
        assert json_data["messages"] == ["Test message"]
        assert json_data["max_tokens"] == 100
        assert json_data["temperature"] == 0.5

        # Test excluding None values
        json_data_exclude_none = request.model_dump(exclude_none=True)
        assert "top_p" not in json_data_exclude_none
        assert "stop" not in json_data_exclude_none
        assert "logprobs" not in json_data_exclude_none

        # Test JSON string serialization
        json_str = request.model_dump_json()
        assert "gpt-3.5-turbo" in json_str
        assert "Test message" in json_str

    def test_field_validation(self) -> None:
        """Test CompletionRequest field validation."""
        # LiteLLM CompletionRequest is more permissive than expected
        # Test that extreme values are accepted (LiteLLM handles validation internally)
        request1 = CompletionRequest(
            model="gpt-3.5-turbo",
            temperature=2.5  # LiteLLM allows this
        )
        assert request1.temperature == 2.5

        request2 = CompletionRequest(
            model="gpt-3.5-turbo",
            max_tokens=-1  # LiteLLM allows this
        )
        assert request2.max_tokens == -1

        # Test boundary values
        request3 = CompletionRequest(
            model="gpt-4",
            temperature=0.0,
            top_p=1.0,
            n=1,
            max_tokens=1
        )
        assert request3.temperature == 0.0
        assert request3.top_p == 1.0
        assert request3.n == 1
        assert request3.max_tokens == 1

    def test_files_field(self) -> None:
        """Test CompletionRequest files field."""
        # Empty files list
        request1 = CompletionRequest(model="gpt-4", files=[])
        assert request1.files == []

        # Single file
        request2 = CompletionRequest(
            model="gpt-4",
            messages=["Analyze this file"],
            files=["s3://bucket/doc.pdf"]
        )
        assert request2.files == ["s3://bucket/doc.pdf"]
        assert len(request2.files) == 1

        # Multiple files
        files = ["s3://bucket/doc1.pdf", "s3://bucket/doc2.txt", "https://example.com/doc3.docx"]
        request3 = CompletionRequest(
            model="gpt-4",
            messages=["Analyze these files"],
            files=files
        )
        assert request3.files == files
        assert len(request3.files) == 3

    def test_edge_case_values(self, edge_case_strings: Dict[str, str]) -> None:
        """Test CompletionRequest with edge case values."""
        # Empty model string (should fail)
        with pytest.raises(ValidationError):
            CompletionRequest(model="")

        # Unicode model name
        request1 = CompletionRequest(model="gpt-4-测试")
        assert request1.model == "gpt-4-测试"

        # Edge case messages
        request2 = CompletionRequest(
            model="gpt-3.5-turbo",
            messages=[edge_case_strings["unicode"], edge_case_strings["special_chars"]]
        )
        assert len(request2.messages) == 2

        # Very long message
        request3 = CompletionRequest(
            model="gpt-4",
            messages=[edge_case_strings["very_long"]]
        )
        assert len(request3.messages[0]) == 1000


class TestChatCompletionRequest:
    """Test cases for ChatCompletionRequest model."""

    def test_minimal_creation(self, sample_chat_completion_request: Dict[str, Any]) -> None:
        """Test creating ChatCompletionRequest with minimal required fields."""
        request = ChatCompletionRequest(
            model=sample_chat_completion_request["model"],
            messages=sample_chat_completion_request["messages"]
        )

        assert request.model == sample_chat_completion_request["model"]
        assert request.messages == sample_chat_completion_request["messages"]
        assert request.max_tokens is None
        assert request.temperature is None
        assert request.top_p is None
        assert request.n is None
        assert request.stream is None
        assert request.stop is None
        assert request.presence_penalty is None
        assert request.frequency_penalty is None
        assert request.logit_bias is None
        assert request.user is None

    def test_complex_messages(self) -> None:
        """Test ChatCompletionRequest with complex message structures."""
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "What's the weather like?"},
            {"role": "assistant", "content": "I don't have access to current weather data."},
            {"role": "user", "content": "Can you help with something else?"}
        ]

        request = ChatCompletionRequest(
            model="gpt-4",
            messages=messages,
            max_tokens=200,
            temperature=0.8
        )

        assert request.model == "gpt-4"
        assert len(request.messages) == 4
        assert request.messages[0]["role"] == "system"
        assert request.messages[0]["content"] == "You are a helpful assistant."
        assert request.messages[-1]["role"] == "user"
        assert request.max_tokens == 200
        assert request.temperature == 0.8

    def test_missing_required_fields(self) -> None:
        """Test ChatCompletionRequest validation with missing required fields."""
        # Missing model
        with pytest.raises(ValidationError, match=r"Field required") as exc_info:
            ChatCompletionRequest(messages=[{"role": "user", "content": "Hi"}])

        assert "model" in str(exc_info.value)

        # Missing messages
        with pytest.raises(ValidationError, match=r"Field required") as exc_info:
            ChatCompletionRequest(model="gpt-3.5-turbo")

        assert "messages" in str(exc_info.value)

        # Both missing
        with pytest.raises(ValidationError):
            ChatCompletionRequest()

    def test_empty_messages(self) -> None:
        """Test ChatCompletionRequest with empty messages list."""
        # Our ChatCompletionRequest allows empty messages (no validation constraint)
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=[]  # Empty messages allowed
        )

        assert request.model == "gpt-3.5-turbo"
        assert request.messages == []

    def test_json_serialization(self) -> None:
        """Test ChatCompletionRequest JSON serialization."""
        messages = [{"role": "user", "content": "Test message"}]
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=messages,
            temperature=0.7
        )

        json_data = request.model_dump()
        assert json_data["model"] == "gpt-3.5-turbo"
        assert json_data["messages"] == messages
        assert json_data["temperature"] == 0.7

        # Test excluding None values
        json_data_exclude_none = request.model_dump(exclude_none=True)
        assert "max_tokens" not in json_data_exclude_none
        assert "top_p" not in json_data_exclude_none
        assert "logprobs" not in json_data_exclude_none

        # Test JSON string serialization
        json_str = request.model_dump_json()
        assert "gpt-3.5-turbo" in json_str
        assert "Test message" in json_str

    def test_optional_parameters(self) -> None:
        """Test ChatCompletionRequest with various optional parameters."""
        messages = [{"role": "user", "content": "Hello"}]
        request = ChatCompletionRequest(
            model="gpt-4",
            messages=messages,
            max_tokens=150,
            temperature=0.9,
            top_p=0.95,
            n=1,
            stream=False,
            stop=["END", "STOP"],
            presence_penalty=0.1,
            frequency_penalty=0.2,
            logit_bias={"token_123": 0.5, "token_456": -0.3},
            user="test_user"
        )

        assert request.max_tokens == 150
        assert request.temperature == 0.9
        assert request.top_p == 0.95
        assert request.n == 1
        assert request.stream is False
        assert request.stop == ["END", "STOP"]
        assert request.presence_penalty == 0.1
        assert request.frequency_penalty == 0.2
        assert request.logit_bias == {"token_123": 0.5, "token_456": -0.3}
        assert request.user == "test_user"

    def test_message_validation(self, edge_case_strings: Dict[str, str]) -> None:
        """Test ChatCompletionRequest message validation."""
        # Valid message structures
        valid_messages = [
            [{"role": "user", "content": "Simple message"}],
            [
                {"role": "system", "content": "System prompt"},
                {"role": "user", "content": "User message"},
                {"role": "assistant", "content": "Assistant response"}
            ],
            [{"role": "user", "content": edge_case_strings["unicode"]}],
            [{"role": "user", "content": edge_case_strings["special_chars"]}]
        ]

        for messages in valid_messages:
            request = ChatCompletionRequest(model="gpt-3.5-turbo", messages=messages)
            assert request.messages == messages

        # Test very long content
        long_message = [{"role": "user", "content": edge_case_strings["very_long"]}]
        request = ChatCompletionRequest(model="gpt-4", messages=long_message)
        assert len(request.messages[0]["content"]) == 1000

    def test_parameter_boundaries(self) -> None:
        """Test ChatCompletionRequest parameter boundary values."""
        messages = [{"role": "user", "content": "Test"}]

        # Test boundary values that should be accepted
        request = ChatCompletionRequest(
            model="gpt-4",
            messages=messages,
            temperature=0.0,  # Minimum
            top_p=1.0,        # Maximum
            n=1,              # Minimum
            max_tokens=1,     # Minimum
            presence_penalty=-2.0,   # Minimum
            frequency_penalty=2.0    # Maximum
        )

        assert request.temperature == 0.0
        assert request.top_p == 1.0
        assert request.n == 1
        assert request.max_tokens == 1
        assert request.presence_penalty == -2.0
        assert request.frequency_penalty == 2.0


class TestLLMModelsIntegration:
    """Integration tests for LLM models."""

    def test_model_compatibility(self) -> None:
        """Test that both models work with same model names."""
        model_name = "gpt-3.5-turbo"

        completion_request = CompletionRequest(
            model=model_name,
            messages=["Complete this"]
        )

        chat_request = ChatCompletionRequest(
            model=model_name,
            messages=[{"role": "user", "content": "Chat with me"}]
        )

        assert completion_request.model == chat_request.model == model_name

    def test_serialization_consistency(self) -> None:
        """Test that both models serialize consistently."""
        completion_request = CompletionRequest(
            model="gpt-4",
            messages=["Test message"],
            temperature=0.7,
            max_tokens=100
        )

        chat_request = ChatCompletionRequest(
            model="gpt-4",
            messages=[{"role": "user", "content": "Test message"}],
            temperature=0.7,
            max_tokens=100
        )

        completion_json = completion_request.model_dump()
        chat_json = chat_request.model_dump()

        # Both should have consistent field names and types
        assert completion_json["model"] == chat_json["model"]
        assert completion_json["temperature"] == chat_json["temperature"]
        assert completion_json["max_tokens"] == chat_json["max_tokens"]

        # Test JSON string serialization consistency
        completion_json_str = completion_request.model_dump_json()
        chat_json_str = chat_request.model_dump_json()

        assert "gpt-4" in completion_json_str
        assert "gpt-4" in chat_json_str
        assert "0.7" in completion_json_str
        assert "0.7" in chat_json_str

    def test_realistic_usage_patterns(self) -> None:
        """Test realistic usage patterns for both models."""
        # Completion for text generation
        completion = CompletionRequest(
            model="gpt-3.5-turbo",
            messages=["Write a short story about"],
            max_tokens=500,
            temperature=0.8,
            files=["s3://bucket/context.txt"]
        )

        # Chat for conversation
        chat = ChatCompletionRequest(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a creative writing assistant."},
                {"role": "user", "content": "Help me write a story."}
            ],
            max_tokens=500,
            temperature=0.8
        )

        assert completion.model == "gpt-3.5-turbo"
        assert chat.model == "gpt-4"
        assert completion.temperature == chat.temperature == 0.8
        assert completion.max_tokens == chat.max_tokens == 500
        assert completion.files == ["s3://bucket/context.txt"]
        assert len(chat.messages) == 2

    def test_parameter_overlap(self) -> None:
        """Test that common parameters work consistently across models."""
        common_params = {
            "model": "gpt-4",
            "max_tokens": 200,
            "temperature": 0.5,
            "top_p": 0.9,
            "n": 1,
            "stream": False,
            "stop": ["END"],
            "presence_penalty": 0.1,
            "frequency_penalty": 0.2,
            "user": "test_user"
        }

        completion = CompletionRequest(
            messages=["Test completion"],
            **common_params
        )

        chat = ChatCompletionRequest(
            messages=[{"role": "user", "content": "Test chat"}],
            **common_params
        )

        # Verify all common parameters are set correctly
        for param, value in common_params.items():
            assert getattr(completion, param) == value
            assert getattr(chat, param) == value

    def test_serialization_roundtrip(
        self,
        sample_completion_request: Dict[str, Any],
        sample_chat_completion_request: Dict[str, Any]
    ) -> None:
        """Test serialization roundtrip for both models."""
        import json

        # Test CompletionRequest roundtrip
        completion = CompletionRequest(**sample_completion_request)
        completion_json = completion.model_dump_json()
        completion_data = json.loads(completion_json)
        recreated_completion = CompletionRequest(**completion_data)

        assert recreated_completion.model == completion.model
        assert recreated_completion.messages == completion.messages
        assert recreated_completion.max_tokens == completion.max_tokens
        assert recreated_completion.temperature == completion.temperature

        # Test ChatCompletionRequest roundtrip
        chat = ChatCompletionRequest(**sample_chat_completion_request)
        chat_json = chat.model_dump_json()
        chat_data = json.loads(chat_json)
        recreated_chat = ChatCompletionRequest(**chat_data)

        assert recreated_chat.model == chat.model
        assert recreated_chat.messages == chat.messages
        assert recreated_chat.max_tokens == chat.max_tokens
        assert recreated_chat.temperature == chat.temperature

    def test_model_field_differences(self) -> None:
        """Test differences between CompletionRequest and ChatCompletionRequest."""
        # CompletionRequest has files field, ChatCompletionRequest doesn't
        completion = CompletionRequest(
            model="gpt-4",
            messages=["Test"],
            files=["s3://bucket/file.pdf"]
        )

        chat = ChatCompletionRequest(
            model="gpt-4",
            messages=[{"role": "user", "content": "Test"}]
        )

        # CompletionRequest should have files
        assert hasattr(completion, 'files')
        assert completion.files == ["s3://bucket/file.pdf"]

        # ChatCompletionRequest should not have files
        assert not hasattr(chat, 'files')

        # Both should have common fields
        common_fields = ['model', 'max_tokens', 'temperature', 'top_p', 'n',
                         'stream', 'stop', 'presence_penalty', 'frequency_penalty', 'user']
        for field in common_fields:
            assert hasattr(completion, field)
            assert hasattr(chat, field)
