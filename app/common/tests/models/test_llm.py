"""Concise tests for common.models.llm module."""

import pytest
from pydantic import ValidationError

from common.models.llm import CompletionRequest, ChatCompletionRequest


class TestCompletionRequest:
    """Test cases for CompletionRequest model."""

    def test_minimal_creation(self) -> None:
        """Test creating CompletionRequest with minimal required fields."""
        request = CompletionRequest(model="gpt-3.5-turbo")

        assert request.model == "gpt-3.5-turbo"
        assert request.files is None
        assert request.max_tokens is None
        assert request.temperature is None

    def test_with_all_fields(self) -> None:
        """Test CompletionRequest with all fields populated."""
        request = CompletionRequest(
            model="gpt-4",
            messages=["Test message"],
            max_tokens=100,
            temperature=0.7,
            files=["s3://bucket/doc.pdf"]
        )

        assert request.model == "gpt-4"
        assert request.messages == ["Test message"]
        assert request.max_tokens == 100
        assert request.temperature == 0.7
        assert request.files == ["s3://bucket/doc.pdf"]

    def test_missing_required_fields(self) -> None:
        """Test CompletionRequest validation with missing required fields."""
        with pytest.raises(ValidationError, match=r"Field required"):
            CompletionRequest()  # Missing model

    def test_json_serialization(self) -> None:
        """Test CompletionRequest JSON serialization."""
        request = CompletionRequest(
            model="gpt-3.5-turbo",
            messages=["Test message"],
            max_tokens=100
        )

        json_data = request.model_dump()
        assert json_data["model"] == "gpt-3.5-turbo"
        assert json_data["messages"] == ["Test message"]
        assert json_data["max_tokens"] == 100

        # Test excluding None values
        json_data_exclude_none = request.model_dump(exclude_none=True)
        assert "temperature" not in json_data_exclude_none


class TestChatCompletionRequest:
    """Test cases for ChatCompletionRequest model."""

    def test_minimal_creation(self) -> None:
        """Test creating ChatCompletionRequest with minimal required fields."""
        messages = [{"role": "user", "content": "Test message"}]
        request = ChatCompletionRequest(
            model="gpt-4",
            messages=messages
        )

        assert request.model == "gpt-4"
        assert request.messages == messages
        assert request.max_tokens is None
        assert request.temperature is None

    def test_with_optional_fields(self) -> None:
        """Test ChatCompletionRequest with optional fields."""
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello"}
        ]
        request = ChatCompletionRequest(
            model="gpt-4",
            messages=messages,
            max_tokens=150,
            temperature=0.7
        )

        assert request.model == "gpt-4"
        assert len(request.messages) == 2
        assert request.max_tokens == 150
        assert request.temperature == 0.7

    def test_missing_required_fields(self) -> None:
        """Test ChatCompletionRequest validation with missing required fields."""
        # Missing model
        with pytest.raises(ValidationError, match=r"Field required"):
            ChatCompletionRequest(messages=[{"role": "user", "content": "Hi"}])

        # Missing messages
        with pytest.raises(ValidationError, match=r"Field required"):
            ChatCompletionRequest(model="gpt-3.5-turbo")

    def test_json_serialization(self) -> None:
        """Test ChatCompletionRequest JSON serialization."""
        messages = [{"role": "user", "content": "Test message"}]
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=messages,
            temperature=0.7
        )

        json_data = request.model_dump()
        assert json_data["model"] == "gpt-3.5-turbo"
        assert json_data["messages"] == messages
        assert json_data["temperature"] == 0.7

        # Test excluding None values
        json_data_exclude_none = request.model_dump(exclude_none=True)
        assert "max_tokens" not in json_data_exclude_none


class TestLLMModelsIntegration:
    """Integration tests for LLM models."""

    def test_model_compatibility(self) -> None:
        """Test that both models work with same model names."""
        model_name = "gpt-3.5-turbo"

        completion_request = CompletionRequest(
            model=model_name,
            messages=["Complete this"]
        )

        chat_request = ChatCompletionRequest(
            model=model_name,
            messages=[{"role": "user", "content": "Chat with me"}]
        )

        assert completion_request.model == chat_request.model == model_name

    def test_serialization_consistency(self) -> None:
        """Test that both models serialize consistently."""
        completion_request = CompletionRequest(
            model="gpt-4",
            messages=["Test message"],
            temperature=0.7
        )

        chat_request = ChatCompletionRequest(
            model="gpt-4",
            messages=[{"role": "user", "content": "Test message"}],
            temperature=0.7
        )

        completion_json = completion_request.model_dump()
        chat_json = chat_request.model_dump()

        # Both should have consistent field names and types
        assert completion_json["model"] == chat_json["model"]
        assert completion_json["temperature"] == chat_json["temperature"]

    def test_files_field_support(self) -> None:
        """Test that both models support files field."""
        completion = CompletionRequest(
            model="gpt-4",
            messages=["Test"],
            files=["s3://bucket/file.pdf"]
        )

        chat = ChatCompletionRequest(
            model="gpt-4",
            messages=[{"role": "user", "content": "Test"}],
            files=["s3://bucket/file.pdf"]
        )

        # Both should have files field
        assert hasattr(completion, 'files')
        assert completion.files == ["s3://bucket/file.pdf"]
        assert hasattr(chat, 'files')
        assert chat.files == ["s3://bucket/file.pdf"]
