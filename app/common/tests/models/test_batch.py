"""Tests for common.models.batch module."""

import uuid
from typing import Any, Dict, List

import pytest
from pydantic import ValidationError

from common.models.batch import JobResponse, JobStatusResponse, JobResultResponse


class TestJobResponse:
    """Test JobResponse model."""

    def test_valid_creation(self, sample_job_ids: Dict[str, str]) -> None:
        """Test creating JobResponse with valid UUID."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        assert response.job_id == job_id
        assert isinstance(response.job_id, uuid.UUID)

    def test_string_uuid_conversion(self, sample_job_ids: Dict[str, str]) -> None:
        """Test JobResponse accepts string UUID."""
        job_id_str = sample_job_ids["uuid_job"]
        response = JobResponse(job_id=job_id_str)

        assert response.job_id == uuid.UUID(job_id_str)
        assert isinstance(response.job_id, uuid.UUID)

    def test_validation_errors(self, invalid_uuid_strings: List[str]) -> None:
        """Test JobResponse validation failures."""
        # Test invalid UUID strings
        for invalid_uuid in invalid_uuid_strings:
            with pytest.raises(ValidationError, match=r"Input should be a valid UUID"):
                JobResponse(job_id=invalid_uuid)

        # Test missing job_id
        with pytest.raises(ValidationError, match=r"Field required"):
            JobResponse()

    def test_json_serialization(self) -> None:
        """Test JobResponse JSON serialization."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        # Test JSON string serialization
        json_str = response.model_dump_json()
        assert str(job_id) in json_str
        assert '"job_id"' in json_str

        # Test dict serialization
        json_dict = response.model_dump()
        assert json_dict["job_id"] == str(job_id)  # UUID serialized as string

    def test_model_config(self) -> None:
        """Test JobResponse model configuration."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        # Test that UUID is properly encoded in JSON
        json_dict = response.model_dump()
        assert isinstance(json_dict["job_id"], str)
        assert json_dict["job_id"] == str(job_id)

    def test_equality_and_hashing(self) -> None:
        """Test JobResponse equality and hashing."""
        job_id = uuid.uuid4()
        response1 = JobResponse(job_id=job_id)
        response2 = JobResponse(job_id=job_id)
        response3 = JobResponse(job_id=uuid.uuid4())

        assert response1 == response2
        assert response1 != response3
        assert hash(response1) == hash(response2)
        assert hash(response1) != hash(response3)


class TestJobStatusResponse:
    """Test JobStatusResponse model."""

    def test_minimal_creation(self, sample_job_ids: Dict[str, str]) -> None:
        """Test creating JobStatusResponse with required fields only."""
        job_id = sample_job_ids["string_job"]
        response = JobStatusResponse(job_id=job_id, status="PENDING")

        assert response.job_id == job_id
        assert response.status == "PENDING"
        assert response.progress == {}
        assert response.status_description is None
        assert response.created_at is None
        assert response.updated_at is None
        assert response.started_at is None
        assert response.completed_at is None

    def test_with_optional_fields(self, sample_batch_status_data: Dict[str, Any]) -> None:
        """Test JobStatusResponse with optional fields."""
        response = JobStatusResponse(**sample_batch_status_data)

        assert response.job_id == sample_batch_status_data["job_id"]
        assert response.status == sample_batch_status_data["status"]
        assert response.status_description == sample_batch_status_data["status_description"]
        assert response.progress == sample_batch_status_data["progress"]
        assert response.created_at == sample_batch_status_data["created_at"]
        assert response.updated_at == sample_batch_status_data["updated_at"]
        assert response.started_at == sample_batch_status_data["started_at"]
        assert response.completed_at == sample_batch_status_data["completed_at"]

    def test_validation_errors(self) -> None:
        """Test JobStatusResponse validation failures."""
        # Missing job_id
        with pytest.raises(ValidationError, match=r"Field required"):
            JobStatusResponse(status="PENDING")

        # Missing status
        with pytest.raises(ValidationError, match=r"Field required"):
            JobStatusResponse(job_id="test-job")

        # Both missing
        with pytest.raises(ValidationError):
            JobStatusResponse()

    def test_progress_field_types(self) -> None:
        """Test JobStatusResponse progress field with various types."""
        # Empty dict (default)
        response1 = JobStatusResponse(job_id="test", status="PENDING")
        assert response1.progress == {}

        # Dict with various data types
        complex_progress = {
            "processed": 75,
            "total": 100,
            "percentage": 75.0,
            "current_item": "item_75",
            "errors": ["error1", "error2"],
            "metadata": {"batch_size": 10, "started": True}
        }
        response2 = JobStatusResponse(
            job_id="test",
            status="IN_PROGRESS",
            progress=complex_progress
        )
        assert response2.progress == complex_progress

    def test_status_values(self) -> None:
        """Test JobStatusResponse with various status values."""
        valid_statuses = [
            "PENDING", "IN_PROGRESS", "COMPLETED", "FAILED",
            "CANCELLED", "PARTIALLY_COMPLETED"
        ]

        for status in valid_statuses:
            response = JobStatusResponse(job_id="test", status=status)
            assert response.status == status

    def test_json_serialization(self, sample_batch_status_data: Dict[str, Any]) -> None:
        """Test JobStatusResponse JSON serialization."""
        response = JobStatusResponse(**sample_batch_status_data)

        # Test dict serialization
        json_dict = response.model_dump()
        assert json_dict["job_id"] == sample_batch_status_data["job_id"]
        assert json_dict["status"] == sample_batch_status_data["status"]
        assert json_dict["progress"] == sample_batch_status_data["progress"]

        # Test JSON string serialization
        json_str = response.model_dump_json()
        assert sample_batch_status_data["job_id"] in json_str
        assert sample_batch_status_data["status"] in json_str

        # Test excluding None values
        json_dict_exclude_none = response.model_dump(exclude_none=True)
        assert "completed_at" not in json_dict_exclude_none  # Should be None


class TestJobResultResponse:
    """Test JobResultResponse model."""

    def test_minimal_creation(self, sample_job_ids: Dict[str, str]) -> None:
        """Test creating JobResultResponse with required fields only."""
        job_id = sample_job_ids["string_job"]
        response = JobResultResponse(job_id=job_id, status="COMPLETED")

        assert response.job_id == job_id
        assert response.status == "COMPLETED"
        assert response.result_urls == []
        assert response.pagination is None

    def test_with_optional_fields(self, sample_batch_result_data: Dict[str, Any]) -> None:
        """Test JobResultResponse with optional fields."""
        response = JobResultResponse(**sample_batch_result_data)

        assert response.job_id == sample_batch_result_data["job_id"]
        assert response.status == sample_batch_result_data["status"]
        assert response.result_urls == sample_batch_result_data["result_urls"]
        assert response.pagination == sample_batch_result_data["pagination"]
        assert len(response.result_urls) == 2

    def test_validation_errors(self) -> None:
        """Test JobResultResponse validation failures."""
        # Missing status
        with pytest.raises(ValidationError, match=r"Field required"):
            JobResultResponse(job_id="test")

        # Missing job_id
        with pytest.raises(ValidationError, match=r"Field required"):
            JobResultResponse(status="COMPLETED")

        # Both missing
        with pytest.raises(ValidationError):
            JobResultResponse()

    def test_result_urls_field(self) -> None:
        """Test JobResultResponse result_urls field with various inputs."""
        # Empty list (default)
        response1 = JobResultResponse(job_id="test", status="COMPLETED")
        assert response1.result_urls == []

        # Single URL
        response2 = JobResultResponse(
            job_id="test",
            status="COMPLETED",
            result_urls=["s3://bucket/result.json"]
        )
        assert len(response2.result_urls) == 1
        assert response2.result_urls[0] == "s3://bucket/result.json"

        # Multiple URLs
        urls = [
            "s3://bucket/result1.json",
            "s3://bucket/result2.json",
            "https://api.example.com/results/123"
        ]
        response3 = JobResultResponse(
            job_id="test",
            status="COMPLETED",
            result_urls=urls
        )
        assert response3.result_urls == urls

    def test_pagination_field(self) -> None:
        """Test JobResultResponse pagination field with various inputs."""
        # None (default)
        response1 = JobResultResponse(job_id="test", status="COMPLETED")
        assert response1.pagination is None

        # Simple pagination
        simple_pagination = {"page": 1, "total": 100}
        response2 = JobResultResponse(
            job_id="test",
            status="COMPLETED",
            pagination=simple_pagination
        )
        assert response2.pagination == simple_pagination

        # Complex pagination
        complex_pagination = {
            "page": 2,
            "per_page": 50,
            "total": 1000,
            "pages": 20,
            "has_next": True,
            "has_prev": True,
            "next_page": 3,
            "prev_page": 1
        }
        response3 = JobResultResponse(
            job_id="test",
            status="COMPLETED",
            pagination=complex_pagination
        )
        assert response3.pagination == complex_pagination

    def test_json_serialization(self, sample_batch_result_data: Dict[str, Any]) -> None:
        """Test JobResultResponse JSON serialization."""
        response = JobResultResponse(**sample_batch_result_data)

        # Test dict serialization
        json_dict = response.model_dump()
        assert json_dict["job_id"] == sample_batch_result_data["job_id"]
        assert json_dict["status"] == sample_batch_result_data["status"]
        assert json_dict["result_urls"] == sample_batch_result_data["result_urls"]
        assert json_dict["pagination"] == sample_batch_result_data["pagination"]

        # Test JSON string serialization
        json_str = response.model_dump_json()
        assert sample_batch_result_data["job_id"] in json_str
        assert sample_batch_result_data["status"] in json_str

        # Test excluding None values
        minimal_response = JobResultResponse(job_id="test", status="COMPLETED")
        json_dict_exclude_none = minimal_response.model_dump(exclude_none=True)
        assert "pagination" not in json_dict_exclude_none  # Should be None


class TestBatchModelsIntegration:
    """Integration tests for batch models."""

    def test_model_consistency(self, sample_job_ids: Dict[str, str]) -> None:
        """Test consistency across batch models."""
        job_id_str = sample_job_ids["string_job"]
        job_id_uuid = uuid.UUID(sample_job_ids["uuid_job"])

        # JobResponse uses UUID
        job_response = JobResponse(job_id=job_id_uuid)

        # Other models use string
        status_response = JobStatusResponse(job_id=job_id_str, status="PENDING")
        result_response = JobResultResponse(job_id=job_id_str, status="COMPLETED")

        # Verify types
        assert isinstance(job_response.job_id, uuid.UUID)
        assert isinstance(status_response.job_id, str)
        assert isinstance(result_response.job_id, str)

        # Verify string representations match
        assert str(job_response.job_id) == sample_job_ids["uuid_job"]
        assert status_response.job_id == job_id_str
        assert result_response.job_id == job_id_str

    def test_realistic_workflow(self) -> None:
        """Test realistic batch job workflow."""
        # 1. Job submission
        job_id = uuid.uuid4()
        submission_response = JobResponse(job_id=job_id)

        # 2. Job status check (pending)
        status_response = JobStatusResponse(
            job_id=str(job_id),
            status="PENDING",
            progress={}
        )

        # 3. Job status check (in progress)
        progress_response = JobStatusResponse(
            job_id=str(job_id),
            status="IN_PROGRESS",
            status_description="Processing items",
            progress={"processed": 50, "total": 100}
        )

        # 4. Job completion
        result_response = JobResultResponse(
            job_id=str(job_id),
            status="COMPLETED",
            result_urls=["s3://results/output.json"],
            pagination={"page": 1, "total": 100}
        )

        # Verify workflow consistency
        assert str(submission_response.job_id) == status_response.job_id
        assert status_response.job_id == progress_response.job_id
        assert progress_response.job_id == result_response.job_id

    def test_serialization_roundtrip(self, sample_batch_result_data: Dict[str, Any]) -> None:
        """Test serialization roundtrip for all models."""
        import json

        # Test JobResponse
        job_response = JobResponse(job_id=uuid.uuid4())
        job_json = job_response.model_dump_json()
        job_data = json.loads(job_json)
        recreated_job = JobResponse(**job_data)
        assert recreated_job.job_id == job_response.job_id

        # Test JobResultResponse
        result_response = JobResultResponse(**sample_batch_result_data)
        result_json = result_response.model_dump_json()
        result_data = json.loads(result_json)
        recreated_result = JobResultResponse(**result_data)
        assert recreated_result.job_id == result_response.job_id
        assert recreated_result.result_urls == result_response.result_urls
